### 待补充的逻辑

~~无人机~~

~~留资错误上报~~

~~一键登录回填用户数据，偶数回填“驾考学员”~~

~~二次确认弹窗按钮（埋点）~~

~~二次确认弹窗的样式~~

~~推荐弹窗type === 'phone' 和 type === 'rankJiaxiao' 时的数据处理~~

埋点梳理

~~abtest全部放到hook里面~~

abtest的citycode取值梳理

~~定位逻辑~~

~~老版本 地图页调用 showRequestDialog(true)（新版本地图页授权功能没做）~~

测试在ios上，tokenLead传false抓包显示0的问题

~~126验证码加载失败的处理~~

~~断网提交~~

~~去除摩托车弹窗~~

已登录用户，表单校验不校验驾照类型？

### 埋点改动

~~二次确认弹窗，旧版进入页面就拉取abtest然后埋点，新版提交才拉取abtest~~