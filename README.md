```mermaid
flowchart TD
    A[进入新手引导] --> B[点击开始]
    B --> C[填写个人信息]
    C --> D{信息校验}
    D -->|校验失败| E[提示错误信息]
    E --> C
    D -->|校验成功| F[选择学车地址]
    F --> G[选择驾照类型]
    G --> H[同意隐私政策]
    H --> I{是否已登录}
    I -->|未登录| J[阿里云手机号校验]
    J --> K{校验结果}
    K -->|通过| L[直接登录]
    K -->|失败| M[验证码登录]
    M --> N[输入验证码]
    N --> O{验证码校验}
    O -->|失败| P[重新输入]
    P --> N
    O -->|成功| Q[登录成功]
    L --> Q
    I -->|已登录| Q
    Q --> R[获取基础参数]
    R --> S[准备提交数据]
    S --> T{是否分步提交}
    T -->|是| U[分步提交流程]
    T -->|否| V[直接提交]
    U --> W[通知客户端订单号]
    W --> X[封装提交数据]
    V --> X
    X --> Y[发起网络请求]
    Y --> Z{提交结果}
    Z -->|成功| AA[提交成功]
    Z -->|失败| BB[提交失败]
    BB --> CC[保存错误请求]
    CC --> DD[显示错误提示]
    AA --> EE[通知客户端完成]
    EE --> FF[结束流程]
    DD --> FF

    subgraph "表单校验"
        D1[手机号校验]
        D2[姓名校验]
        D3[地址校验]
        D4[驾照类型校验]
        D5[隐私政策校验]
        D --> D1
        D1 --> D2
        D2 --> D3
        D3 --> D4
        D4 --> D5
    end

    subgraph "登录验证"
        J1[MCProtocol.Core.User.numAuth]
        J --> J1
        J1 --> K
    end

    subgraph "数据提交"
        S1[生成订单号]
        S2[封装用户信息]
        S3[封装地址信息]
        S4[封装驾照信息]
        S --> S1
        S1 --> S2
        S2 --> S3
        S3 --> S4
        S4 --> T
    end
```