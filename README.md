```mermaid
flowchart TD
    A[进入新手引导页面] --> B[点击开始]
    B --> C[填写个人信息表单]

    C --> D1[手机号输入]
    C --> D2[姓名输入]
    C --> D3[选择学车地址]
    C --> D4[选择驾照类型]
    C --> D5[同意隐私政策]

    D1 --> E{表单校验}
    D2 --> E
    D3 --> E
    D4 --> E
    D5 --> E

    E -->|校验失败| F[显示错误提示]
    F --> C

    E -->|校验成功| G{用户登录状态检查}

    G -->|未登录| H[启动登录流程]
    G -->|已登录| M[获取用户信息]

    H --> I[阿里云手机号认证]
    I --> J{认证结果}

    J -->|认证成功| K[一键登录成功]
    J -->|认证失败| L[验证码登录]

    L --> L1[发送验证码]
    L1 --> L2[用户输入验证码]
    L2 --> L3{验证码校验}
    L3 -->|失败| L4[重新输入]
    L4 --> L2
    L3 -->|成功| K

    K --> M
    M --> N[准备提交数据]

    N --> O[生成订单号]
    O --> P[封装用户数据]
    P --> Q[封装地址数据]
    Q --> R[封装驾照数据]
    R --> S[获取定位信息]
    S --> T[获取精度信息]

    T --> U{提交方式判断}

    U -->|分步提交| V[分步提交流程]
    U -->|直接提交| W[直接提交流程]

    V --> V1[通知客户端订单号]
    V1 --> V2[封装分步数据]
    V2 --> X[发起网络请求]

    W --> W1[封装完整数据]
    W1 --> X

    X --> Y{网络请求结果}

    Y -->|请求成功| Z{业务结果判断}
    Y -->|请求失败| AA[网络异常处理]

    Z -->|业务成功| BB[提交成功]
    Z -->|业务失败| CC[业务失败处理]

    AA --> DD[保存错误请求]
    CC --> DD
    DD --> EE[显示错误提示]

    BB --> FF[通知客户端完成]
    FF --> GG[页面跳转或关闭]

    EE --> HH[用户重试或退出]

    subgraph "表单验证模块"
        E1[手机号格式验证]
        E2[姓名格式验证]
        E3[地址必填验证]
        E4[驾照类型验证]
        E5[隐私政策验证]
    end

    subgraph "登录认证模块"
        I1[MCProtocol认证接口]
        I2[验证码服务接口]
        I3[用户信息获取]
    end

    subgraph "数据提交模块"
        S1[数据封装服务]
        S2[网络请求服务]
        S3[错误处理服务]
    end
```